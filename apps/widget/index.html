<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sample HTML Page</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
      }

      .container {
        max-width: 600px;
        margin: auto;
        background-color: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      img {
        max-width: 100%;
        border-radius: 4px;
      }

      button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
      }

      button:hover {
        background-color: #0056b3;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Welcome to My Website</h1>
      <p>This is a simple HTML example with a title, image, and button.</p>
      <img
        src="https://placehold.co/600x400?text=Hello\nWorld"
        alt="Sample Image"
      />
      <p>
        <button id="send-message-button">Send Message from Outside</button>
      </p>
    </div>
    <div class="saas-ai-chatbot-widget"></div>

    <script type="module" src="/src/main.tsx"></script>
    <script type="module">
      window.SaasAiChatbotWidget.render("aac42829-694c-4edf-89c7-f83df0e3247b");
      document
        .getElementById("send-message-button")
        .addEventListener("click", () => {
          window.SaasAiChatbotWidget.sendMessage("Hello");
        });
    </script>
  </body>
</html>
