import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'
import { name } from './package.json'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  define: {
    'process.env': {},
  },
  build: {
    lib: {
      entry: './src/main.tsx',
      name: name,
      fileName: name,
      formats: ['iife'],
    },
    rollupOptions: {
      output: {
        entryFileNames: 'caps-widget.js',
        extend: true,
      },
    },
    minify: true,
  },
})

