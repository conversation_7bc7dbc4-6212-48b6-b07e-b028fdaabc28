import ReactDOM from "react-dom/client";
import Widget from "../../../packages/chatbot-widget/src/Widget";
import { ChatbotProvider } from "../../../packages/chatbot-widget/src/context/chatbot-provider";
import {
  setChatbotId,
  setCustomerId,
} from "../../../packages/chatbot-widget/src/global";
import mainStyles from "../../../packages/chatbot-widget/src/styles/main.css?inline";
import templateStyles from "../../../packages/chatbot-widget/src/styles/template.css?inline";
import { ApiError } from "../../../packages/chatbot-widget/src/lib/make-api";
import { mergeConfigIfTargetMissing } from "../../../packages/chatbot-widget/src/lib/merge-config";
import { storage } from "../../../packages/chatbot-widget/src/lib/storage";
import { ChatService } from "../../../packages/chatbot-widget/src/services/chat.service";
import { ThemeService } from "../../../packages/chatbot-widget/src/services/theme.service";

async function createCustomer() {
  const groupName = import.meta.env.VITE_CHATBOT_GROUP_NAME;
  const customer = await ChatService.createCustomer();
  storage.setToGroup(groupName, "customer", customer);
  setCustomerId(customer.id);
}

async function checkCustomer() {
  const groupName = import.meta.env.VITE_CHATBOT_GROUP_NAME;
  const customer = storage.getFromGroup(groupName, "customer");

  try {
    if (!customer) {
      await createCustomer();
    } else {
      setCustomerId(customer?.id);
      const customerResponse = await ChatService.getCustomer(customer?.id);
      if (!customerResponse) {
        await createCustomer();
      }
    }
  } catch (error) {
    console.error(error);
    if (error instanceof ApiError) {
      if (error.status === 400 || error.status === 404) {
        await createCustomer();
      } else {
        throw error;
      }
    }
  }
}

async function checkConversation() {
  const groupName = import.meta.env.VITE_CHATBOT_GROUP_NAME;
  try {
    const conversation = storage.getFromGroup(groupName, "conversation");
    if (!conversation) {
      return;
    }

    await ChatService.getConversation(conversation.id);
  } catch (error) {
    console.log(error);
    if (error instanceof ApiError) {
      if (error.status === 404 || error.status === 400) {
        storage.removeFromGroup(groupName, "conversation");
      }
    }
  }
}

let onSendMessageRef: ((message: string) => void) | null = null;

async function render(id: string) {
  if (id) {
    try {
      const container = document.createElement("div");
      container.id = "saas-ai-chatbot-widget-container";
      setChatbotId(id);

      const config = await ChatService.getConfig();
      mergeConfigIfTargetMissing(config, {
        appearance: {
          theme: {
            background: "#ffffff",
            foreground: "#262626",
            primary: "#f59e0b",
            primaryForeground: "#000000",
            secondary: "#f3f4f6",
            secondaryForeground: "#4b5563",
            muted: "#f3f4f6",
            mutedForeground: "#6b7280",
            border: "#e5e7eb",
            input: "#e5e7eb",
            radius: "16px",
            shadow:
              "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
            shadowLg:
              "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
            shadowMd:
              "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
          },
          widget: {
            toggleText: "Chat with meee",
            placeholder: "Type your message...",
            welcomeMessage: "Hi, how can I help you today?",
            suggestions: ["Hello", "How are you?", "What is your name?"],
          },
        },
      });

      await checkCustomer();

      await checkConversation();

      const shadowRoot = container.attachShadow({ mode: "open" });

      // Apply theme first
      ThemeService.applyThemeToShadowRoot(config.appearance.theme, shadowRoot);

      // Inject styles into shadow DOM
      const styleSheet = new CSSStyleSheet();
      styleSheet.replaceSync(mainStyles);
      shadowRoot.adoptedStyleSheets = [styleSheet];
      const styleSheetTemplate = new CSSStyleSheet();
      styleSheetTemplate.replaceSync(templateStyles);
      shadowRoot.adoptedStyleSheets = [styleSheet, styleSheetTemplate];

      // Create a div inside shadow root for React to render into
      const mountPoint = document.createElement("div");
      shadowRoot.appendChild(mountPoint);

      // Append the container to document body
      document.body.appendChild(container);

      // Create root and render inside shadow DOM
      const root = ReactDOM.createRoot(mountPoint);
      root.render(
        <ChatbotProvider config={config}>
          <Widget
            onInit={(sendMessage) => {
              onSendMessageRef = sendMessage;
            }}
          />
        </ChatbotProvider>
      );
    } catch (error) {
      console.error(error);
    }
  }
}

function sendMessage(message: string) {
  if (onSendMessageRef) {
    onSendMessageRef(message);
  } else {
    console.warn("Chatbot is not initialized yet");
  }
}

declare global {
  interface Window {
    SaasAiChatbotWidget: {
      render: (id: string) => void;
      sendMessage: (message: string) => void;
    };
  }
}

// Attach the function to window for external access
window.SaasAiChatbotWidget = {
  render,
  sendMessage,
};
