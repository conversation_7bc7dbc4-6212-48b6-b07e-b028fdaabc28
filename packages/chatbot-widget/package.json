{"name": "@thecapsai/chatbot-widget", "version": "2.0.7", "type": "module", "main": "dist/caps-widget.umd.js", "module": "dist/caps-widget.es.js", "types": "dist/caps-widget.es.d.ts", "style": "dist/chatbot-widget.css", "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "exports": {"./chatbot-widget.css": "./dist/chatbot-widget.css", ".": {"import": "./dist/caps-widget.es.js", "require": "./dist/caps-widget.umd.js"}}, "files": ["dist"], "scripts": {"dev": "vite build --watch", "build": "npm run clean && vite build", "prepublishOnly": "npm run build", "clean": "rm -rf dist", "lint": "eslint .", "preview": "vite preview"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "dependencies": {"marked": "^16.0.0"}, "devDependencies": {"@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.5.2", "typescript": "~5.8.3", "vite": "^7.0.0", "vite-plugin-dts": "^4.5.4"}, "sideEffects": ["*.css"]}