import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'
import dts from 'vite-plugin-dts'
import { name } from './package.json'

// https://vite.dev/config/
export default defineConfig({
    plugins: [
        react(),
        dts({
            insertTypesEntry: true,
            tsconfigPath: './tsconfig.json',
            rollupTypes: true
        })
    ],
    define: {
        'process.env': {},
    },
    build: {
        lib: {
            entry: './src/main.ts',
            name: name,
            formats: ['es', 'umd'],
            fileName: (format) => `caps-widget.${format}.js`,
        },
        rollupOptions: {
            external: ['react', 'react-dom', 'react/jsx-runtime'],
            output: {
                globals: {
                    react: 'React',
                    'react-dom': 'ReactDOM',
                    'react/jsx-runtime': 'React',
                },
                extend: true,
                inlineDynamicImports: true,
            },
        },
        minify: true,
        sourcemap: true,
    },
})
