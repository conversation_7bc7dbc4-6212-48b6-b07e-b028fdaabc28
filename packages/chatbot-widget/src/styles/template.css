.template-container {
  max-width: 90%;
}

/* Form Template Styles */
.template-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: white;
  border-radius: calc(var(--radius) / 2);
  box-shadow: 1px 1px 3px #0000000d, -1px -1px 3px #0000000d;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  text-transform: capitalize;
}

.field-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: calc(var(--radius) / 2);
  font-size: 14px;
}

.field-input:focus {
  outline: none;
  border-color: var(--primary);
}

.required {
  color: #dc3545;
}

.submit-button {
  padding: 8px 16px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: calc(var(--radius) / 2);
  cursor: pointer;
  font-size: 14px;
}

.submit-button:hover {
  opacity: 0.8;
}

/* Product Template Styles */
.template-product {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px;
  background: white;
  border-radius: calc(var(--radius) / 2);
  box-shadow: 1px 1px 3px #0000000d, -1px -1px 3px #0000000d;
}

.product-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.product-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
}

/* 2 lines */
.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-price {
  font-size: 18px;
  font-weight: 500;
  color: var(--primary);
  margin: 0;
}

/* 3 lines */
.product-description {
  font-size: 14px;
  color: #666;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.action-button {
  padding: 8px 16px;
  border-radius: calc(var(--radius) / 2);
  cursor: pointer;
  font-size: 14px;
}

.action-button.view-details {
  background: white;
  border: 1px solid var(--primary);
  color: var(--primary);
}

.action-button.view-details:hover {
  opacity: 0.8;
}

.action-button.add-to-cart {
  background: var(--primary);
  border: none;
  color: white;
}

.action-button.add-to-cart:hover {
  background: var(--primary);
  opacity: 0.8;
}

.multiple-products {
  display: grid;
  gap: 16px;
}

.multiple-products.two-columns {
  grid-template-columns: repeat(2, 1fr);
}

@media screen and (max-width: 768px) {
  .product-description {
    display: none;
  }
}
