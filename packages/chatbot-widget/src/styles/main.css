.widget-container {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
}

.chat-toggle-container {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;

  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 12px;
}

.chat-widget {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
}

.chat-suggestions {
  align-items: flex-end;
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 280px;
}

.suggestion-btn {
  background: var(--background);
  color: var(--foreground);
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 6px 12px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  box-shadow: var(--shadow);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  opacity: 0;
  transform: translateY(10px);
  animation: slideInWithFade 0.5s ease-out forwards;
  position: relative;
}

.suggestion-btn::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: var(--radius);
  padding: 2px;
  background: linear-gradient(45deg, var(--primary), transparent);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.suggestion-btn:hover {
  background: var(--background);
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-lg);
}

.suggestion-btn:hover::before {
  opacity: 1;
}

/* Add animation delay for each suggestion button */
.suggestion-btn:nth-child(1) {
  animation-delay: 0.1s;
}

.suggestion-btn:nth-child(2) {
  animation-delay: 0.2s;
}

.suggestion-btn:nth-child(3) {
  animation-delay: 0.3s;
}

.suggestion-btn:nth-child(4) {
  animation-delay: 0.4s;
}

.suggestion-btn:nth-child(5) {
  animation-delay: 0.5s;
}

@keyframes slideInWithFade {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--primary);
  color: var(--primary-foreground);
  border: none;
  border-radius: var(--radius);
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.chat-toggle:hover {
  transform: translateY(-1px);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
}

.chat-widget {
  width: 480px;
  height: 700px;
  background: var(--background);
  border-radius: var(--radius);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.chat-profile {
  padding: 12px;
  border-bottom: 1px solid var(--border);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  display: flex;
  align-items: center;
}

.profile-info {
  display: flex;
  gap: 12px;
  align-items: center;
}

.profile-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-number {
  margin: 0;
  font-size: 14px;
  color: var(--muted-foreground);
  margin-top: 4px;
}

.profile-details {
  flex: 1;
}

.profile-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--foreground);
  line-height: 1.2;
}

.profile-email {
  margin: 2px 0 0 0;
  font-size: 14px;
  color: var(--muted-foreground);
}

.profile-actions {
  display: flex;
  gap: 8px;
}

.btn {
  background: none;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  color: var(--muted-foreground);
  transition: all 0.2s ease;
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn:hover {
  background: var(--muted);
  color: var(--foreground);
}

.btn svg {
  width: 20px;
  height: 20px;
}

.chat-messages-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
  display: flex;
  flex-direction: column-reverse;
  gap: 16px;
}

.chat-message {
  /* max-width: 85%; */
  animation: messageIn 0.3s ease-out;
}

@keyframes messageIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-message.bot {
  align-self: flex-start;
  max-width: 90%;
}

.chat-message.user {
  align-self: flex-end;
  max-width: 85%;
}

.message-content {
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
}

.chat-message.bot .message-content {
  background: var(--muted);
  color: var(--foreground);
  border-bottom-left-radius: 6px;
}

.chat-message.user .message-content {
  background: var(--primary);
  color: var(--primary-foreground);
  border-bottom-right-radius: 6px;
}

.message-content p {
  margin: 0;
}

.timestamp {
  font-size: 11px;
  color: var(--muted-foreground);
  margin-top: 4px;
  text-align: right;
}

.chat-message.bot .timestamp {
  text-align: left;
}

.chat-input {
  padding: 12px;
  border-top: 1px solid var(--border);
  display: flex;
  gap: 12px;
  align-items: center;
}

.chat-input input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid var(--border);
  background: var(--input);
  color: var(--foreground);
  border-radius: 24px;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
  resize: none;
  min-height: 20px;
  max-height: 100px;
}

.chat-input input:focus {
  border-color: var(--primary);
  background: var(--background);
}

.chat-input input::placeholder {
  color: var(--muted-foreground);
}

.send-btn {
  width: 40px;
  height: 40px;
  background: var(--primary);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: var(--primary-foreground);
}

.send-btn:hover:not(:disabled) {
  background: var(--primary);
  transform: scale(1.05);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.typing-indicator {
  align-self: flex-start;
  background: var(--muted);
  padding: 12px 16px;
  border-radius: 18px;
  border-bottom-left-radius: 6px;
  display: flex;
  gap: 4px;
}

.typing-dot {
  width: 6px;
  height: 6px;
  background: var(--muted-foreground);
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}
.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%,
  60%,
  100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-8px);
    opacity: 1;
  }
}

.scroll-to-bottom-btn {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: var(--primary);
  color: var(--primary-foreground);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow);
  transition: all 0.2s ease;
  z-index: 10;
  animation: fadeIn 0.2s ease-out;
}

.scroll-to-bottom-btn:hover {
  background: var(--primary);
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.fadeIn {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUpMobile {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Styles */
@media screen and (max-width: 768px) {
  .chat-widget {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100vh;
    border-radius: 0;
    animation: slideUpMobile 0.3s ease-out;
  }

  .chat-profile {
    padding: 16px;
  }

  .chat-messages {
    padding: 16px;
  }

  .chat-input {
    padding: 16px;
    padding-bottom: max(16px, env(safe-area-inset-bottom));
  }
}
