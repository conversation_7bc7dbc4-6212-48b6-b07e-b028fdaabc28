import { useEffect, useState } from "react";
import { storage } from "../lib/storage";

export const useStateStorage = <T>(
    initialValue: T,
    key: string,
    group = "default",
): [T, (value: T) => void] => {
    const [state, _setState] = useState<T>(() => {
        const item = storage.get(group) || "{}";
        if (item) {
            try {
                const parsedItem = JSON.parse(item);
                return parsedItem[key] || initialValue;
            } catch {
                return initialValue;
            }
        }
        return initialValue;
    });

    const setState = (value: T) => {
        _setState(value);
        const item = storage.get(group) || "{}";
        const parsedItem = JSON.parse(item);
        parsedItem[key] = value;
        storage.set(group, JSON.stringify(parsedItem));
    };

    useEffect(() => {
        const handleStorage = (e: StorageEvent) => {
            if (e.key === group && e.newValue !== e.oldValue) {
                try {
                    const parsed = JSON.parse(e.newValue ?? "{}");
                    if (parsed[key] !== undefined) {
                        _setState(parsed[key]);
                    }
                } catch {
                    console.log("error");
                }
            }
        };

        window.addEventListener("storage", handleStorage);
        return () => window.removeEventListener("storage", handleStorage);
    }, [group, key]);

    return [state, setState];
};