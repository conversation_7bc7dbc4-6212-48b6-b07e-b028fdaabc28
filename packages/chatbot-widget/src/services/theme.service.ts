export interface ThemeConfig {
  background?: string;
  foreground?: string;
  primary?: string;
  primaryForeground?: string;
  secondary?: string;
  secondaryForeground?: string;
  muted?: string;
  mutedForeground?: string;
  accent?: string;
  accentForeground?: string;
  destructive?: string;
  destructiveForeground?: string;
  border?: string;
  input?: string;
  ring?: string;
  radius?: string;
  shadow?: string;
  shadowMd?: string;
  shadowLg?: string;
}

const getThemeStyle = (target: 'host' | 'root', config: ThemeConfig) => {
  return `
    :${target} {
        --background: ${config.background};
        --foreground: ${config.foreground};
        --primary: ${config.primary};
        --primary-foreground: ${config.primaryForeground};
        --secondary: ${config.secondary};
        --secondary-foreground: ${config.secondaryForeground};
        --muted: ${config.muted};
        --muted-foreground: ${config.mutedForeground};
        --accent: ${config.accent};
        --accent-foreground: ${config.accentForeground};
        --destructive: ${config.destructive};
        --destructive-foreground: ${config.destructiveForeground};
        --border: ${config.border};
        --input: ${config.input};
        --ring: ${config.ring};
        --radius: ${config.radius};
        --shadow: ${config.shadow};
        --shadow-md: ${config.shadowMd};
        --shadow-lg: ${config.shadowLg};
    }
  `;
};

export const ThemeService = {
  applyThemeToShadowRoot(config: ThemeConfig, shadowRoot: ShadowRoot) {
    const style = document.createElement("style");
    style.textContent = getThemeStyle('host', config);
    shadowRoot.appendChild(style);
  },
  applyThemeToDocument(id: string, config: ThemeConfig, document: Document) {
    let style = document.getElementById(id);
    style = style ?? document.createElement("style");
    style.id = id;
    style.textContent = getThemeStyle('root', config);
    document.head.appendChild(style);
  },
};
