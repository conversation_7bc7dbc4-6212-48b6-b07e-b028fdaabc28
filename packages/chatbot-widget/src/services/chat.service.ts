import { getChatbotId, getConversationId, getCustomerId } from "../global";
import { makeApi } from "../lib/make-api";
import { join } from "../lib/path";
import type { ChatbotConfig, Conversation, Customer, MessagesQuery, MessagesResponse } from "../type";

const API_URL = import.meta.env.VITE_API_URL;

export const ChatService = {
    getConfig: (): Promise<ChatbotConfig> => {
        return makeApi(`${API_URL}/widget/${getChatbotId()}/config`);
    },
    createConversation: (): Promise<Conversation> => {
        return makeApi(`${API_URL}/widget/${getChatbotId()}/conversations`, {
            method: "POST",
        });
    },
    getConversation: (conversationId?: string | null): Promise<Conversation | null> => {
        return makeApi(`${API_URL}/widget/${getChatbotId()}/conversations/${conversationId ?? getConversationId()}`);
    },
    getMessages: (query: MessagesQuery = {
        limit: 10,
        skip: 0,
    }): Promise<MessagesResponse> => {
        const url = new URL(join(API_URL, `/widget/${getChatbotId()}/conversations/${getConversationId()}/messages`));
        url.searchParams.set("limit", query.limit.toString());
        url.searchParams.set("skip", query.skip.toString());

        return makeApi(url.toString(), {
            method: "GET",
        });
    },
    getCustomer: (customerId: string): Promise<Customer> => {
        return makeApi(`${API_URL}/widget/${getChatbotId()}/customers/${customerId}`);
    },
    createCustomer: (): Promise<Customer> => {
        return makeApi(`${API_URL}/widget/${getChatbotId()}/customers`, {
            method: "POST"
        });
    },
    getAnswerStream: async (message: string, onMessage?: (message: string) => void) => {
        const url = new URL(join(API_URL, `/widget/${getChatbotId()}/conversations/${getConversationId()}/answer`));
        url.searchParams.set("message", message);

        const response = await fetch(url.toString(), {
            method: 'GET',
            headers: {
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache',
                'x-customer-id': getCustomerId(),
            },
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        if (!response.body) {
            throw new Error('Response body is null');
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        const stream = new ReadableStream({
            async start(controller) {
                try {
                    while (true) {
                        const { done, value } = await reader.read();

                        if (done) {
                            controller.close();
                            break;
                        }

                        const chunk = decoder.decode(value, { stream: true });
                        const lines = chunk.split('\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.slice(6); // Remove 'data: ' prefix
                                if (data.trim() !== '') {
                                    if (onMessage) {
                                        onMessage(data);
                                    }
                                    controller.enqueue(data);
                                }
                            }
                        }
                    }
                } catch (error) {
                    controller.error(error);
                } finally {
                    reader.releaseLock();
                }
            },
        });

        return stream;
    },
};