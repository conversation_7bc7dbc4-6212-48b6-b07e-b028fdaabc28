import { useEffect, useRef, useState } from "react";
import ChatInput from "./components/chat-input";
import ChatMessage from "./components/chat-message";
import { Icons } from "./components/icons";
import Loading from "./components/loading";
import { TypingIndicator } from "./components/typing-indicator";
import { getConversationId } from "./global";
import { useChatbot } from "./hooks/use-chatbot";
import { formatDatetime } from "./lib/format-datetime";

interface WidgetProps {
  onInit?: (sendMessage: (message: string) => void) => void;
}

function Widget({ onInit }: WidgetProps) {
  const {
    config,
    messages,
    isOpen,
    onToggleOpen,
    state,
    isLoading,
    onLoadMore,
    isEnd,
    onSendMessage,
    onClearConversation,
    conversation,
  } = useChatbot();

  useEffect(() => {
    if (onInit) {
      onInit(onSendMessage);
    }
  }, [onInit, onSendMessage]);

  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);

  useEffect(() => {
    if (!isOpen) {
      // Add a small delay before showing suggestions
      const timer = setTimeout(() => {
        setShowSuggestions(true);
      }, 500);
      return () => clearTimeout(timer);
    } else {
      setShowSuggestions(false);
    }
  }, [isOpen]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && !isLoading && !isEnd) {
          onLoadMore();
        }
      },
      {
        root: messagesContainerRef.current,
        threshold: 0.1,
      }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [isLoading, onLoadMore, isEnd]);

  useEffect(() => {
    if (!isOpen) return;

    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isNearBottom =
        scrollTop < 0 && scrollHeight - scrollTop - clientHeight > 150;
      setShowScrollButton(isNearBottom);
    };

    handleScroll();
    container.addEventListener("scroll", handleScroll);
  }, [isOpen, messagesContainerRef]);

  const scrollToBottom = () => {
    if (messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      container.scrollTo({
        top: container.scrollHeight,
        behavior: "smooth",
      });
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    onSendMessage(suggestion);
    onToggleOpen();
  };

  return (
    <div className="widget-container">
      {isOpen ? (
        <div className="chat-widget">
          <div className="chat-profile">
            <div className="profile-info">
              <div className="profile-avatar">
                <img
                  src={
                    config.appearance.logo ||
                    "http://cdn.thecapsai.com/images/logo.png"
                  }
                  alt={config.name}
                />
              </div>
              <div className="profile-details">
                <h3 className="profile-name">{config.name}</h3>
                {conversation?.idNumber && (
                  <p className="profile-number">#{conversation?.idNumber}</p>
                )}
              </div>
            </div>
            <div className="profile-actions">
              <button className="btn" onClick={onClearConversation}>
                <Icons.refresh />
              </button>
              <button className="close-btn btn" onClick={onToggleOpen}>
                <Icons.close />
              </button>
            </div>
          </div>

          <div className="chat-messages-container">
            <div className="chat-messages" ref={messagesContainerRef}>
              {state === "typing" && <TypingIndicator />}
              {messages.map((message) => (
                <ChatMessage
                  key={message.id}
                  message={message.content}
                  isUser={message.role === "user"}
                  timestamp={formatDatetime(message.createdAt)}
                  data={{
                    action: message.action ?? null,
                    actionMetadata: message.actionMetadata ?? null,
                    intent: message.intent ?? null,
                    intentMetadata: message.intentMetadata ?? null,
                  }}
                />
              ))}
              {config.appearance.widget.welcomeMessage && (
                <ChatMessage
                  key={"WELCOME_MESSAGE" + getConversationId()}
                  message={config.appearance.widget.welcomeMessage ?? ""}
                  isUser={false}
                />
              )}
              {isLoading && <Loading />}
              <div ref={loadMoreRef} className="load-more-trigger" />
            </div>

            {showScrollButton && (
              <button className="scroll-to-bottom-btn" onClick={scrollToBottom}>
                <Icons.chevronDown />
              </button>
            )}
          </div>

          <ChatInput placeholder={config.appearance.widget.placeholder} />
        </div>
      ) : (
        <div className="chat-toggle-container">
          {showSuggestions &&
            config.appearance.widget.suggestions &&
            config.appearance.widget.suggestions.length > 0 && (
              <div className="chat-suggestions">
                {config.appearance.widget.suggestions.map(
                  (suggestion, index) => (
                    <button
                      key={index}
                      className="suggestion-btn"
                      onClick={() => handleSuggestionClick(suggestion)}
                    >
                      {suggestion}
                    </button>
                  )
                )}
              </div>
            )}
          <button className="chat-toggle slide-in" onClick={onToggleOpen}>
            <Icons.message />
            {config.appearance.widget.toggleText && (
              <span>{config.appearance.widget.toggleText}</span>
            )}
          </button>
        </div>
      )}
    </div>
  );
}

export default Widget;
