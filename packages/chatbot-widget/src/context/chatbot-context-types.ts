import type { ChatbotConfig, Conversation, Message, State } from "../type";

export interface ChatbotContextType {
    config: ChatbotConfig;
    conversation?: Conversation;
    messages: Message[];
    onSendMessage: (message: string) => void;
    onCreateConversation: () => Promise<void>;
    isOpen: boolean;
    onToggleOpen: () => void;
    state: State;
    isEnd: boolean;
    isLoading: boolean;
    onLoadMore: () => void;
    onClearConversation: () => void;
}

export const initState: ChatbotContextType = {
    config: {} as ChatbotConfig,
    conversation: undefined,
    messages: [] as Message[],
    onSendMessage: () => { },
    onCreateConversation: () => Promise.resolve(),
    isOpen: false,
    onToggleOpen: () => { },
    state: "idle",
    isEnd: false,
    isLoading: false,
    onLoadMore: () => { },
    onClearConversation: () => { },
}; 