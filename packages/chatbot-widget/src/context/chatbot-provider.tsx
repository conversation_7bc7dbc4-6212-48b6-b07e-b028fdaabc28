import type { ReactNode } from "react";
import { useCallback, useEffect, useRef, useState } from "react";
import { getConversationId, setConversationId } from "../global";
import { useStateStorage } from "../hooks/use-state-storage";
import { uuid } from "../lib/generate-uuid";
import { ChatService } from "../services/chat.service";
import type {
  AnswerStream,
  ChatbotConfig,
  Conversation,
  Message,
  State,
} from "../type";
import ChatbotContext from "./chatbot-context";

interface ChatbotProviderProps {
  children: ReactNode;
  config: ChatbotConfig;
}

export const ChatbotProvider = ({ children, config }: ChatbotProviderProps) => {
  const [conversation, setConversation] = useStateStorage<
    Conversation | undefined
  >(undefined, "conversation", import.meta.env.VITE_CHATBOT_GROUP_NAME);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isOpen, setIsOpen] = useStateStorage(
    false,
    "isOpen",
    import.meta.env.VITE_CHATBOT_GROUP_NAME
  );
  const [state, _setState] = useState<State>("idle");
  const [isEnd, setIsEnd] = useState(false);
  const [isLoading, _setIsLoading] = useState(false);
  const isLoadingRef = useRef(isLoading);
  // if conversation is not set, it means the first load (don't need to first load)
  const isFirstLoadRef = useRef(!!conversation?.id);
  const stateRef = useRef(state);

  const setState = useCallback((state: State) => {
    stateRef.current = state;
    _setState(state);
  }, []);

  const setIsLoading = useCallback((isLoading: boolean) => {
    isLoadingRef.current = isLoading;
    _setIsLoading(isLoading);
  }, []);

  useEffect(() => {
    if (conversation?.id) {
      setConversationId(conversation.id);
    }
  }, [conversation]);

  const onToggleOpen = useCallback(() => {
    setIsOpen(!isOpen);
  }, [isOpen]);

  const onCreateConversation = useCallback(async () => {
    const conversation = await ChatService.createConversation();
    setConversationId(conversation.id);
    setConversation(conversation);

    setState("idle");
    setIsEnd(false);
    setIsLoading(false);
    isLoadingRef.current = false;
  }, [setConversation, setIsLoading, setState]);

  const onMessage = useCallback(
    (message: string) => {
      const answerStream = JSON.parse(message) as AnswerStream;

      if (answerStream.type) {
        switch (answerStream.type) {
          case "status":
            console.log(answerStream);
            break;
          case "delta": {
            switch (stateRef.current) {
              case "idle":
                break;
              case "typing": {
                setState("streaming");
                // create a new message
                const newMessage: Message = {
                  id: uuid(),
                  role: "assistant",
                  content: answerStream.text,
                  conversationId: getConversationId(),
                  createdAt: new Date().toISOString(),
                  intent: null,
                  action: null,
                };
                setMessages((prev) => [newMessage, ...prev]);
                break;
              }
              case "streaming": {
                setMessages((prev) => {
                  const newMessages = [...prev];
                  const lastMessage = newMessages[0];
                  lastMessage.content += answerStream.text;
                  return newMessages;
                });
                break;
              }
              default:
                break;
            }
            break;
          }
          case "complete":
            setMessages((prev) => {
              const newMessages = [...prev];
              const lastMessage = newMessages[0];
              lastMessage.intent = answerStream.data.intent;
              lastMessage.intentMetadata = answerStream.data.intentMetadata;
              return newMessages;
            });
            console.log("complete", answerStream.data);
            setState("idle");
            return;
        }
      }
    },
    [setState]
  );

  const onClearConversation = useCallback(() => {
    setMessages([]);
    setConversation(undefined);
    setState("idle");
    setIsEnd(false);
  }, [setConversation, setState, setIsEnd]);

  const onSendMessage = useCallback(
    async (message: string) => {
      if (stateRef.current !== "idle") {
        return;
      }

      const newMessage: Message = {
        id: uuid(),
        role: "user",
        content: message,
        conversationId: getConversationId(),
        createdAt: new Date().toISOString(),
        intent: null,
        action: null,
      };
      setMessages((prev) => [newMessage, ...prev]);

      if (!conversation) {
        await onCreateConversation();
      }

      setState("typing");
      ChatService.getAnswerStream(message, onMessage);
    },
    [conversation, onCreateConversation, onMessage, setState]
  );

  const onLoadMore = useCallback(async () => {
    if (isEnd || isLoadingRef.current || !conversation?.id) return;
    try {
      setIsLoading(true);
      isLoadingRef.current = true;
      const res = await ChatService.getMessages({
        skip: messages.length,
        limit: 10,
      });
      setMessages((prev) => [...prev, ...res.items]);
      setIsEnd(res.meta.total < res.meta.skip + res.meta.limit);
    } catch (error) {
      console.error(error);
    } finally {
      isLoadingRef.current = false;
      setIsLoading(false);
    }
  }, [isEnd, conversation?.id, setIsLoading, messages.length]);

  useEffect(() => {
    if (
      isFirstLoadRef.current &&
      !isLoadingRef.current &&
      conversation?.id &&
      isOpen
    ) {
      onLoadMore();
      isFirstLoadRef.current = false;
    }
  }, [onLoadMore, conversation, isLoadingRef, isOpen]);

  return (
    <ChatbotContext.Provider
      value={{
        config,
        conversation,
        messages,
        onSendMessage,
        onCreateConversation,
        isOpen,
        onToggleOpen,
        state,
        isEnd,
        isLoading,
        onLoadMore,
        onClearConversation,
      }}
    >
      {children}
    </ChatbotContext.Provider>
  );
};
