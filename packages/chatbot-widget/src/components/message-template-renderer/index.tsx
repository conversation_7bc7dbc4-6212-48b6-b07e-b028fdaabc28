import type { MessageTemplate } from "../../type";
import MessageTemplateForm from "./templates/form";
import MessageTemplateProduct from "./templates/product";

type MessageTemplateRendererProps = {
  template: MessageTemplate;
};

function MessageTemplateRenderer({ template }: MessageTemplateRendererProps) {
  switch (template.type) {
    case "form":
      return <MessageTemplateForm template={template} />;
    case "product":
      return <MessageTemplateProduct template={template} />;
  }
}

export default MessageTemplateRenderer;
