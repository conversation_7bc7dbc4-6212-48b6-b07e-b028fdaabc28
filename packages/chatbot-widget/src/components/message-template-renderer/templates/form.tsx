import type { MessageTemplateForm as MessageTemplateFormType } from "../../../type";

type MessageTemplateFormProps = {
  template: MessageTemplateFormType;
};

function MessageTemplateForm({ template }: MessageTemplateFormProps) {
  return (
    <form className="template-form template-container">
      {template.schema.fields.map((field) => (
        <div key={field.name} className="form-field">
          <label htmlFor={field.name} className="field-label">
            {field.name} {field.required && <span className="required">*</span>}
          </label>
          <input
            type={field.type}
            id={field.name}
            name={field.name}
            required={field.required}
            className="field-input"
          />
        </div>
      ))}
      <button type="submit" className="submit-button">
        Submit
      </button>
    </form>
  );
}

export default MessageTemplateForm;
