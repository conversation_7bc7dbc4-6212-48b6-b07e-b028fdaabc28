import type { MessageTemplateProduct as MessageTemplateProductType } from "../../../type";
import { formatCurrency } from "../../../lib/format-currency";
import { useChatbot } from "../../../hooks/use-chatbot";

type MessageTemplateProductProps = {
  template: MessageTemplateProductType;
};

function MessageTemplateProduct({ template }: MessageTemplateProductProps) {
  const { onSendMessage } = useChatbot();
  const { display, actions } = template.schema;
  const { data } = template;

  return (
    <div
      className={`template-container multiple-products ${
        data.products.length > 1 ? "two-columns" : ""
      }`}
    >
      {data.products.map((product) => (
        <div className="template-product" key={product.id}>
          <div
            className="product-content"
            key={product.id}
            onClick={() => {
              console.log(product);
            }}
          >
            {display.includes("imageUrl") && product.imageUrl && (
              <img
                src={product.imageUrl}
                alt={product.imageUrl}
                className="product-image"
              />
            )}
            {display.includes("name") && product.name && (
              <h3 className="product-name">{product.name}</h3>
            )}
            {display.includes("price") && product.price && (
              <p className="product-price">{formatCurrency(product.price)}</p>
            )}
            {display.includes("description") && product.description && (
              <p className="product-description">{product.description}</p>
            )}
          </div>

          <div className="product-actions">
            {actions.includes("view_details") && (
              <button
                className="action-button view-details"
                onClick={() => {
                  onSendMessage(product.name);
                }}
              >
                View Details
              </button>
            )}
            {actions.includes("add_to_cart") && (
              <button className="action-button add-to-cart">Add to Cart</button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
}

export default MessageTemplateProduct;
