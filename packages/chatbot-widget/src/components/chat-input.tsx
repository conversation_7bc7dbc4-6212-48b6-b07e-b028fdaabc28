import { useMemo, useState } from "react";
import type { KeyboardEvent } from "react";
import { useChatbot } from "../hooks/use-chatbot";
import { Icons } from "./icons";

interface ChatInputProps {
  placeholder?: string;
}

const ChatInput: React.FC<ChatInputProps> = ({ placeholder }) => {
  const { onSendMessage, state } = useChatbot();
  const [message, setMessage] = useState("");

  const handleSend = () => {
    if (message.trim()) {
      onSendMessage(message);
      setMessage("");
    }
  };

  const disabled = useMemo(() => {
    return !message.trim() || state !== "idle";
  }, [message, state]);

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && !e.shiftKey && !disabled) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <div className="chat-input">
      <input
        type="text"
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder || "Type your message..."}
        aria-label="Chat message input"
      />
      <button
        className="send-btn"
        onClick={handleSend}
        disabled={disabled}
        aria-label="Send message"
      >
        <Icons.arrowRight />
      </button>
    </div>
  );
};

export default ChatInput;
