import React from "react";
import type { MessageData } from "../type";
import { marked } from "marked";
import MessageTemplateRenderer from "./message-template-renderer";

type ChatMessageProps = {
  message: string;
  isUser: boolean;
  timestamp?: string;
  data?: MessageData;
};

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  isUser,
  timestamp,
  data,
}) => {
  return (
    <>
      {data?.intentMetadata?.template && (
        <MessageTemplateRenderer template={data.intentMetadata.template} />
      )}
      <div className={`chat-message ${isUser ? "user" : "bot"}`}>
        <div className="message-content">
          <p dangerouslySetInnerHTML={{ __html: marked(message) }} />
          {timestamp && <span className="timestamp">{timestamp}</span>}
        </div>
      </div>
    </>
  );
};

export default ChatMessage;
