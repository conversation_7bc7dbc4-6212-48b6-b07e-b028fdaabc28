export const join = (...paths: (string | undefined | null)[]): string => {
    return paths
        .filter(Boolean) // Remove empty, undefined, null
        .map((part, index) => {
            if (index === 0) {
                // Only trim end for the first part to preserve leading slash if present
                return part?.replace(/\/+$/, "") ?? "";
            }
            // Trim both ends for middle parts
            return part?.replace(/^\/+|\/+$/g, "") ?? "";
        })
        .join("/");
};

const path = {
    join,
};

export default path;
