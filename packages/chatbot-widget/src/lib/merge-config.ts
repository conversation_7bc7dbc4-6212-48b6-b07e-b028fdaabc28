import type { ChatbotConfig, DeepPartial } from "../type";

// merge data into target if target is missing (not object)
export function mergeConfigIfTargetMissing(target: ChatbotConfig, data: DeepPartial<ChatbotConfig>) {
    for (const key in data) {
        const k = key as keyof ChatbotConfig;
        if (data[k] && typeof data[k] === 'object' && !Array.isArray(data[k])) {
            if (!target[k] || typeof target[k] !== 'object') {
                target[k] = {} as ChatbotConfig[keyof ChatbotConfig];
            }
            mergeConfigIfTargetMissing(target[k] as ChatbotConfig, data[k] as Partial<ChatbotConfig>);
        } else if (data[k] !== undefined && target[k] === undefined) {
            target[k] = data[k] as ChatbotConfig[Extract<keyof ChatbotConfig, string>];
        }
    }

    return target;
}