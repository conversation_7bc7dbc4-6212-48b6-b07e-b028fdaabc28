import { storage } from "./storage";

const CUSTOMER_HEADER_KEY = "x-customer-id"

export class ApiError extends Error {
    status: number;
    statusText: string;

    constructor(status: number, statusText: string) {
        super(`Request failed with status ${status}: ${statusText}`);
        this.status = status;
        this.statusText = statusText;
    }
}


// allow catch error
export const makeApi = async (...args: Parameters<typeof fetch>) => {
    const groupName = import.meta.env.VITE_CHATBOT_GROUP_NAME;
    const customerId = storage.getFromGroup(groupName, "customer")?.id;
    if (customerId) {
        args[1] = {
            ...args[1],
            headers: {
                ...args[1]?.headers,
                [CUSTOMER_HEADER_KEY]: customerId,
            },
        };
    }
    const response = await fetch(...args);
    if (!response.ok) {
        throw new ApiError(response.status, response.statusText);
    }
    return response.json();
};

