export const uuid = (): string => {
    const bytes = new Uint8Array(16);
    crypto.getRandomValues(bytes);

    // Set version 4 (UUIDv4) => xxxx-xxxx-4xxx
    bytes[6] = (bytes[6] & 0x0f) | 0x40;
    // Set variant (bits 6-7 of byte 8) => 10xx (8, 9, A, B)
    bytes[8] = (bytes[8] & 0x3f) | 0x80;

    const hex = Array.from(bytes).map(b => b.toString(16).padStart(2, '0'));

    return [
        hex.slice(0, 4).join(''),
        hex.slice(4, 6).join(''),
        hex.slice(6, 8).join(''),
        hex.slice(8, 10).join(''),
        hex.slice(10, 16).join('')
    ].join('-');
}