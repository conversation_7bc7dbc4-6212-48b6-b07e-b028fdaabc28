const PREFIX = import.meta.env.VITE_STORAGE_PREFIX;

export const storage = {
    get: (key: string) => {
        return localStorage.getItem(`${PREFIX}:${key}`);
    },
    set: (key: string, value: string) => {
        localStorage.setItem(`${PREFIX}:${key}`, value);
    },
    remove: (key: string) => {
        localStorage.removeItem(`${PREFIX}:${key}`);
    },
    getFromGroup: (group: string, key: string) => {
        const obj = JSON.parse(localStorage.getItem(`${PREFIX}:${group}`) || "{}");
        return obj[key];
    },
    setToGroup: (group: string, key: string, value: string | object) => {
        const obj = JSON.parse(localStorage.getItem(`${PREFIX}:${group}`) || "{}");
        obj[key] = value;
        localStorage.setItem(`${PREFIX}:${group}`, JSON.stringify(obj));
    },
    removeFromGroup: (group: string, key: string) => {
        const obj = JSON.parse(localStorage.getItem(`${PREFIX}:${group}`) || "{}");
        delete obj[key];
        localStorage.setItem(`${PREFIX}:${group}`, JSON.stringify(obj));
    },
};