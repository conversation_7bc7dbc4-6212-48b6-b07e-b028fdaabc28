export const formatDatetime = (date: string) => {
    const dateObj = new Date(date);

    if (dateObj.toDateString() === new Date().toDateString()) {
        return dateObj.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
        });
    }

    return dateObj.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
}