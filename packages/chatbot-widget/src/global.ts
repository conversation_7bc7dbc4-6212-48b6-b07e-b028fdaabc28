const global = {
    chatbotId: "",
    conversationId: "",
    customerId: "",
};

const setChatbotId = (id: string) => {
    global.chatbotId = id;
};

const getChatbotId = () => {
    return global.chatbotId;
};

const setConversationId = (id: string) => {
    global.conversationId = id;
};

const getConversationId = () => {
    return global.conversationId;
};

const setCustomerId = (id: string) => {
    global.customerId = id;
};

const getCustomerId = () => {
    return global.customerId;
};

export { setChatbotId, getChatbotId, setConversationId, getConversationId, setCustomerId, getCustomerId };