import type { ThemeConfig } from "./services/theme.service";

export type State = "idle" | "typing" | "streaming";

export type ChatbotConfig = {
  id: string;
  name: string;
  configuration: any;
  appearance: ChatbotAppearance;
};

export type Customer = {
  id: string;
};

export type Conversation = {
  id: string;
  idNumber: string;
};

export type Role = "system" | "assistant" | "user";

export type Message = {
  id: string;
  role: Role;
  content: string;
  conversationId: string;
  intent: string | null;
  intentMetadata?: any;
  action: string | null;
  actionMetadata?: any;
  metadata?: any;
  createdAt: string;
};

export type MessagesResponse = {
  items: Message[];
  meta: {
    total: number;
    skip: number;
    limit: number;
  };
};

export type MessagesQuery = {
  skip: number;
  limit: number;
};

export type MessageTemplateMetadata = {
  template: MessageTemplate;
};

export type MessageData = {
  intent: string | null;
  intentMetadata: MessageTemplateMetadata;
  action: string | null;
  actionMetadata: any;
};

export type ChatbotAppearance = {
  theme: ThemeConfig;
  widget: {
    toggleText?: string;
    placeholder?: string;
    welcomeMessage?: string;
    suggestions?: string[];
  };
  logo?: string;
};


export type AnswerStream =
  | StreamCompleteData
  | StreamDeltaData
  | StreamStatusData;

type IntentType = string;

export interface StreamCompleteData {
  type: 'complete';
  data: {
    intent: IntentType;
    intentMetadata: Record<string, any>;
  };
}

export interface StreamDeltaData {
  type: 'delta';
  text: string;
}

export interface StreamStatusData {
  type: 'status';
  status: 'start' | 'thinking' | 'summarizing';
}

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type MessageTemplate =
  | MessageTemplateForm
  | MessageTemplateProduct;

export type MessageTemplateForm = {
  type: "form";
  schema: {
    fields: {
      name: string;
      type: "text" | "email" | "date" | "time";
      required: boolean;
    }[];
  };
};

export type MessageTemplateProduct = {
  type: "product";
  schema: {
    display: ("name" | "price" | "description" | "imageUrl")[];
    actions: ("view_details" | "add_to_cart")[];
  };
  data: { products: MessageDataProduct[] }
};

export type MessageDataProduct = {
  id: string;
  name: string;
  price?: number;
  description?: string;
  imageUrl?: string;
};