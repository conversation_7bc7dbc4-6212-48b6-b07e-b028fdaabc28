import "./styles/main.css";
import "./styles/template.css";

export { default as MessageTemplateRenderer } from "./components/message-template-renderer";
export { default as ChatbotContext } from "./context/chatbot-context";
export { ChatbotProvider } from "./context/chatbot-provider";
export { setChatbotId } from "./global";
export { useChatbot } from "./hooks/use-chatbot";
export { ApiError } from "./lib/make-api";
export { mergeConfigIfTargetMissing } from "./lib/merge-config";
export { storage } from "./lib/storage";
export { ChatService } from "./services/chat.service";
export { ThemeService } from "./services/theme.service";
export {
    type ChatbotConfig,
    type Conversation, type DeepPartial, type Message, type MessageData,
    type MessageDataProduct, type MessagesQuery, type MessagesResponse, type MessageTemplate,
    type MessageTemplateMetadata
} from "./type";
export { default as Widget } from "./Widget";