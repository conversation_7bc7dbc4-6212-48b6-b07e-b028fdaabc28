# SaaS AI Chatbot Widget

A lightweight, customizable AI chatbot widget that can be easily embedded into any website. Built with React, TypeScript, and Vite.

## Features

- 🎯 Easy Integration - Simple embed code for any website
- 🛡️ Shadow DOM - Ensures styles don't conflict with your website
- 🎨 Customizable - Adapt the widget to match your brand
- 📱 Responsive - Works seamlessly on desktop and mobile
- 🚀 Lightweight - Minimal impact on page load performance

## Installation

Add the widget to your website by including our script:

```html
<script src="[YOUR_WIDGET_URL]"></script>
```

## Usage

Initialize the widget by adding this code to your website:

```javascript
window.SaasAiChatbotWidget.render("YOUR_WIDGET_ID");
```

Replace `YOUR_WIDGET_ID` with your unique widget identifier from your dashboard.

## Development

### Prerequisites

- Node.js (v16 or higher)
- Yarn or npm

### Setup

1. Clone the repository:

```bash
git clone [repository-url]
cd saas-ai-chatbot-widget
```

2. Install dependencies:

```bash
yarn install
# or
npm install
```

3. Start the development server:

```bash
yarn dev
# or
npm run dev
```

### Building

Build the widget for production:

```bash
yarn build
# or
npm run build
```

## Configuration

The widget can be customized through various options:

```javascript
window.SaasAiChatbotWidget.render("YOUR_WIDGET_ID", {
  // Configuration options will go here
});
```
